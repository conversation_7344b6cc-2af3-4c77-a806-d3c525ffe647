'use client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import type React from 'react';

const LoadingLogoBounding: React.FC = () => {
  const t = useTranslations('workflow');
  return (
    <div className="w-full  bg-white rounded-2xl p-6 space-y-6 m-0">
      {/* Logo + spinner */}
      <div className="flex flex-col items-center space-y-3">
        <div className="relative">
          <Image
            src="/images/logo/minastik-full-ver.png"
            alt="Logo"
            width={80}
            height={80}
            className="rounded-full shadow-sm"
          />
          {/* Spinner vòng quanh logo */}
          <div className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"></div>
        </div>
        <p className="text-gray-600 text-sm animate-pulse flex items-center">
          {t('common.AIWaiting')}
          <span className="!animate-bounce mx-1">.</span>
          <span className="!animate-bounce mx-1 [animation-delay:200ms]">.</span>
          <span className="!animate-bounce mx-1 [animation-delay:400ms]">.</span>
        </p>
      </div>

    </div>

  );
};

export default LoadingLogoBounding;

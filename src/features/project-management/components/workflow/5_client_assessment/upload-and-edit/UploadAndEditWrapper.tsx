import { useEffect, useMemo, useState } from 'react';
import UploadFileResearch from './UploadFileResearch';
import TextUploadWrapper from './TextUploadWrapper';
import { EDeskResearch } from '@/features/project-management/types/questionnaire';
import type { ConversationDataType } from '@/features/project-management/types/questionnaire';
import type { IFileResponse } from '@/shared/types/global';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';

type UploadAndEditType = {
  status: 'upload' | 'editor';
  data: any[];
  id: string;
  stepId: string;
  templates: IFileResponse[];
  evaluationFramework: string;
  nameForm: string;
  conversationData: ConversationDataType[];
  onBackDashboard: () => void;
  onOpenDetailScore: (data: string) => void;
};

const UploadAndEditWrapper: React.FC<UploadAndEditType> = ({
  status,
  data,
  id,
  stepId,
  templates,
  evaluationFramework,
  nameForm,
  conversationData,
  onBackDashboard,
  onOpenDetailScore,
}) => {
  const [view, setView] = useState<'upload' | 'editor'>(() => status);

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setView(status);
  }, [status]);

  const [markdown, setMarkdown] = useState<string>('');

  const [model, setModel] = useState<string>(EValueModelAI.GPT);

  const dataFormFileSearch = useMemo(() => {
    const item = data.find(d => d.type === EDeskResearch.UPLOAD_FILE);
    setModel(item ? item.model : EValueModelAI.GPT);
    return item ? item.infos : [];
  }, [data]);

  return (
    view === 'upload'
      ? (
          <UploadFileResearch
            type={EDeskResearch.UPLOAD_FILE}
            data={dataFormFileSearch}
            id={id}
            stepId={stepId}
            initialData={data}
            model={model}
            changeNextView={setView}
            onBackDashboard={onBackDashboard}
            showViewButton={!!markdown}
            onViewData={() => setView('editor')}
          />
        )
      : (
          <TextUploadWrapper
            id={id}
            stepId={stepId}
            data={data}
            templates={templates}
            nameForm={nameForm}
            conversationData={conversationData}
            evaluationFramework={evaluationFramework}
            onBackDashboard={onBackDashboard}
            onBackUploadFile={() => setView('upload')}
            getMarkdown={setMarkdown}
            onOpenDetailScore={onOpenDetailScore}
          />
        )

  );
};

export default UploadAndEditWrapper;

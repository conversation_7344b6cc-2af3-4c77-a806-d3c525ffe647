'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { updateResearch } from '../services/project.service';
import type { UpdateProjectResearch } from '../types/research';

/**
 * Hook for updating a research item
 *
 * This hook provides a way to update a research item by ID.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Update mutation and helper method
 */
export function useProjectResearchUpdate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Update research mutation
  const updateResearchMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<UpdateProjectResearch>; stepId?: string }) =>
      updateResearch(id, data),
    onSuccess: async (_, variables) => {
      // invalidate queries sau khi cập nhật thành công
      await queryClient.invalidateQueries({ queryKey: ['research'] });
      await queryClient.refetchQueries({ queryKey: ['getInfoDetail', variables.stepId], type: 'all' });
    },
    onError: (error) => {
      console.error('Error updating research:', error);
    },
  });

  // Function to update a research item
  const updateResearchItem = useCallback(async (id: string, data: Partial<UpdateProjectResearch>, stepId?: string) => {
    return updateResearchMutation.mutateAsync({ id, data, stepId });
  }, [updateResearchMutation]);

  return {
    // Mutation state
    isUpdating: updateResearchMutation.isPending,
    updateError: updateResearchMutation.error,

    // Action
    updateResearchItem,
  };
}

import { useQuery } from '@tanstack/react-query';
import { getQAByStepId } from '../services/project.service';

export function useGetInfoDetail<T, D>(selectedStepId: string) {
  return useQuery({
    queryKey: ['getInfoDetail', selectedStepId],
    queryFn: () => getQAByStepId<T, D>(selectedStepId as string),
    select: response => response.data,
    enabled: !!selectedStepId,
    staleTime: 0,
  });
}

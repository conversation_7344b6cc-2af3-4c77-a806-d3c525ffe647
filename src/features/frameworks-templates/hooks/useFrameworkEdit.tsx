'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { FrameworkPayload } from '../types';
import { updateFramework } from '../components/services';

export function useFrameworkEdit() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ payload, id }: { payload: FrameworkPayload; id: string }) => updateFramework(payload, id),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['getFramework'] });
      // toast.success('Project created successfully');
    },
    // Removed onError to allow component-level error handling
  });
}
